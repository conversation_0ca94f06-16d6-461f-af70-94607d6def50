<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Security Scorecard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7fafc; /* Tailwind gray-100 */
        }
        .table-cell-content {
            display: flex;
            align-items: center;
            justify-content: center; /* Center content in cells */
            gap: 0.5rem; /* Space between icon and text */
        }
        .product-area-row td {
            background-color: #e2e8f0; /* Tailwind gray-300 */
            font-weight: 600; /* Tailwind semibold */
            padding-top: 0.75rem; /* py-3 */
            padding-bottom: 0.75rem; /* py-3 */
        }
        th, td {
            padding: 0.75rem; /* p-3 */
            text-align: left;
            border: 1px solid #e2e8f0; /* Tailwind gray-300 */
            font-size: 0.875rem; /* text-sm */
        }
        th {
            background-color: #f1f5f9; /* Tailwind slate-100 */
            font-weight: 600; /* semibold */
            white-space: nowrap; /* Prevent header text from wrapping */
        }
        .metric-maturity-row td {
            font-style: italic;
            font-size: 0.75rem; /* text-xs */
            color: #4a5568; /* Tailwind gray-700 */
            background-color: #f8fafc; /* Tailwind slate-50 */
            text-align: center; /* Center the circle ratings */
        }
        .progress-bar-container {
            width: 100%;
            min-width: 80px; /* Ensure progress bar is visible */
            background-color: #e5e7eb; /* Tailwind gray-200 */
            border-radius: 9999px; /* rounded-full */
            height: 1rem; /* h-4 */
            overflow: hidden; /* Ensure inner bar respects rounded corners */
        }
        .progress-bar {
            height: 1rem; /* h-4 */
            border-radius: 9999px; /* rounded-full */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem; /* text-xs */
            color: white;
            font-weight: 500; /* medium */
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 160px;
            background-color: #2d3748; /* Tailwind gray-800 */
            color: #fff;
            text-align: center;
            border-radius: 0.375rem; /* rounded-md */
            padding: 0.5rem; /* p-2 */
            position: absolute;
            z-index: 10;
            bottom: 125%;
            left: 50%;
            margin-left: -80px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem; /* text-xs */
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        /* Fake Data Banner */
        .fake-data-banner {
            background-color: #fefcbf; /* Tailwind yellow-100 */
            border: 1px solid #fef08a; /* Tailwind yellow-200 */
            color: #b45309; /* Tailwind amber-700 */
            padding: 0.75rem 1.25rem; /* py-3 px-5 */
            margin-bottom: 1.5rem; /* mb-6 */
            border-radius: 0.5rem; /* rounded-lg */
            text-align: center;
            font-weight: 500; /* medium */
        }
        /* Icon colors */
        .icon-green { color: #10b981; } /* Tailwind green-500 */
        .icon-red { color: #ef4444; } /* Tailwind red-500 */
        .icon-yellow { color: #f59e0b; } /* Tailwind amber-500 */
        .icon-blue { color: #3b82f6; } /* Tailwind blue-500 */
        .icon-gray { color: #9ca3af; } /* Tailwind gray-400 */
        .icon-purple { color: #8b5cf6; } /* Tailwind violet-500 */

        /* Responsive table container */
        .table-container {
            overflow-x: auto; /* Allows horizontal scrolling on smaller screens */
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-md */
        }
        table {
            width: 100%;
            border-collapse: collapse; /* Ensures borders are neat */
            min-width: 900px; /* Minimum width before scrolling starts */
        }

        /* Rating Circles for Metric Maturity */
        .rating-circles {
            display: flex;
            align-items: center;
            justify-content: center; /* Center circles within their container */
            gap: 2px; /* Reduced gap for tighter circle spacing */
        }
        .rating-circles .fa-circle, .rating-circles .far.fa-circle { /* Target both solid and regular Font Awesome circles */
            font-size: 0.85rem; /* Slightly larger circles */
            line-height: 1; /* Ensure proper alignment */
        }
        .rating-filled {
            color: #4f46e5; /* Tailwind indigo-600 */
        }
        .rating-empty {
            color: #d1d5db; /* Tailwind gray-300 */
        }
        .rating-text { /* Optional text next to circles e.g. (4/5) */
            font-size: 0.7rem;
            color: #6b7280; /* Tailwind gray-500 */
            margin-left: 0.3rem;
            font-weight: normal; /* Ensure it's not italic if the td is */
        }
        .legend-rating-circles { /* For use in legend */
            display: inline-flex; /* To keep circles in line with text */
            align-items: center;
            gap: 2px;
            margin-right: 0.25rem; /* Space before descriptive text */
        }
        .legend-rating-circles .fa-circle, .legend-rating-circles .far.fa-circle {
            font-size: 0.8rem;
        }

        /* Filter controls styling */
        .filter-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap; /* Allow wrapping on small screens */
        }
        .filter-controls label {
            font-weight: 600;
            color: #4a5568;
        }
        .filter-controls select {
            padding: 0.5rem 0.75rem;
            border: 1px solid #cbd5e0; /* Tailwind gray-300 */
            border-radius: 0.375rem; /* rounded-md */
            background-color: white;
            font-size: 0.875rem;
            outline: none;
            cursor: pointer;
            transition: border-color 0.2s;
        }
        .filter-controls select:focus {
            border-color: #6366f1; /* Tailwind indigo-500 */
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }
    </style>
</head>
<body class="p-4 md:p-8">

    <div class="max-w-7xl mx-auto">
        <header class="mb-6 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-800">Product Security Scorecard</h1>
            <p class="text-slate-600 mt-2">A high-level overview of product security posture and metric maturity.</p>
        </header>

        <div class="fake-data-banner">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Demo Data:</strong> The information presented in this scorecard is illustrative and uses placeholder data.
        </div>

        <section class="mb-8 p-6 bg-white rounded-lg shadow-md">
            <h2 class="text-xl font-semibold text-slate-700 mb-4">Legend</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
                <div>
                    <h3 class="font-semibold text-slate-600 mb-2">Metric Status:</h3>
                    <ul class="space-y-1">
                        <li><span class="table-cell-content justify-start"><i class="fas fa-check-circle icon-green mr-2 w-5 text-center"></i> Meets Bar / Compliant</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-times-circle icon-red mr-2 w-5 text-center"></i> Below Bar / Non-Compliant</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-exclamation-triangle icon-yellow mr-2 w-5 text-center"></i> Partially Compliant / Issues</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-link icon-blue mr-2 w-5 text-center"></i> Review Data / Link</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-pencil-alt icon-gray mr-2 w-5 text-center"></i> Pending Review</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-clock icon-purple mr-2 w-5 text-center"></i> Planned / Future</span></li>
                        <li><span class="table-cell-content justify-start"><i class="fas fa-minus-circle icon-gray mr-2 w-5 text-center"></i> N/A / No Data</span></li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-slate-600 mb-2">Metric Maturity (1-5 Scale):</h3>
                    <ul class="space-y-1.5">
                        <li>
                            <span class="legend-rating-circles">
                                <i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i>
                            </span> (1/5) Future Domain
                        </li>
                        <li>
                            <span class="legend-rating-circles">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i>
                            </span> (2/5) Developing Signal
                        </li>
                        <li>
                            <span class="legend-rating-circles">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i>
                            </span> (3/5) Defined (Manual/Proxy)
                        </li>
                        <li>
                            <span class="legend-rating-circles">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i>
                            </span> (4/5) Potentially Useful Signal
                        </li>
                        <li>
                            <span class="legend-rating-circles">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i>
                            </span> (5/5) Fully Observable & Mature
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-slate-600 mb-2">Overall Grade:</h3>
                    <ul class="space-y-1">
                        <li><span class="font-bold text-green-600">A (Excellent)</span></li>
                        <li><span class="font-bold text-lime-600">B (Good)</span></li>
                        <li><span class="font-bold text-yellow-600">C (Fair)</span></li>
                        <li><span class="font-bold text-orange-600">D (Needs Improvement)</span></li>
                    </ul>
                </div>
            </div>
            <p class="mt-4 text-xs text-slate-500">Progress bars show percentage values. Links <i class="fas fa-link icon-blue"></i> may point to detailed security review documents.</p>
        </section>

        <div class="filter-controls">
            <label for="gradeFilter" class="block text-sm font-medium text-gray-700">Filter by Grade:</label>
            <select id="gradeFilter" name="gradeFilter">
                <option value="All">All Grades</option>
                <option value="A">A (Excellent)</option>
                <option value="B">B (Good)</option>
                <option value="C">C (Fair)</option>
                <option value="D">D (Needs Improvement)</option>
            </select>
        </div>

        <div class="table-container bg-white">
            <table>
                <thead>
                    <tr>
                        <th class="sticky left-0 bg-slate-100 z-10">Product</th>
                        <th>Memory Safety</th>
                        <th>Business Justification</th>
                        <th>Vulnerability Management</th>
                        <th>2SV Enforcement</th>
                        <th>Data Encryption (Rest)</th>
                        <th>Overall Grade</th>
                    </tr>
                    <tr class="metric-maturity-row">
                        <td class="sticky left-0 bg-slate-50 z-10 font-semibold">Metric Maturity</td>
                        <td> <div class="rating-circles" title="5/5: Fully Observable & Mature">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i>
                            </div>
                        </td>
                        <td> <div class="rating-circles" title="3/5: Defined (Manual/Proxy)">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i>
                            </div>
                        </td>
                        <td> <div class="rating-circles" title="4/5: Potentially Useful Signal">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i>
                            </div>
                        </td>
                        <td> <div class="rating-circles" title="1/5: Future Domain">
                                <i class="fas fa-circle rating-filled"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i><i class="far fa-circle rating-empty"></i>
                            </div>
                        </td>
                        <td> <div class="rating-circles" title="5/5: Fully Observable & Mature">
                                <i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i><i class="fas fa-circle rating-filled"></i>
                            </div>
                        </td>
                        <td class="font-normal text-slate-500">Derived Score</td> </tr>
                </thead>
                <tbody>
                    <tr class="product-area-row">
                        <td colspan="7" class="sticky left-0 bg-gray-300 z-10">COMMUNICATION TOOLS</td>
                    </tr>
                    <tr data-grade="A">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Gmail</td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td>
                            <div class="table-cell-content tooltip">
                                <i class="fas fa-link icon-blue"></i> Review Complete
                                <span class="tooltiptext">Security Review #12345 - All clear.</span>
                            </div>
                        </td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-green-500" style="width: 98%;">98%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Fully Enforced</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-green-600">A</div></td>
                    </tr>
                    <tr data-grade="B">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Google Chat</td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-pencil-alt icon-gray"></i> Pending Review</div></td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-yellow-500 text-yellow-800" style="width: 85%;">85%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-exclamation-triangle icon-yellow"></i> Partially</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-lime-600">B</div></td>
                    </tr>
                    <tr data-grade="C">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Google Meet</td>
                        <td><div class="table-cell-content"><i class="fas fa-times-circle icon-red"></i> Fail</div></td>
                        <td>
                            <div class="table-cell-content tooltip">
                                <i class="fas fa-link icon-blue"></i> Review Complete
                                <span class="tooltiptext">Security Review #12347 - Minor issues found.</span>
                            </div>
                        </td>
                        <td>
                               <div class="progress-bar-container">
                                <div class="progress-bar bg-red-500" style="width: 70%;">70%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-clock icon-purple"></i> Planned</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-exclamation-triangle icon-yellow"></i> Fail</div></td>
                        <td><div class="table-cell-content font-bold text-yellow-600">C</div></td>
                    </tr>

                    <tr class="product-area-row">
                        <td colspan="7" class="sticky left-0 bg-gray-300 z-10">CLOUD INFRASTRUCTURE</td>
                    </tr>
                    <tr data-grade="A">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Spanner <span class="text-xs text-gray-500">(Dep)</span></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> N/A</div></td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-green-500" style="width: 100%;">100%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-minus-circle icon-gray"></i> N/A</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-green-600">A</div></td>
                    </tr>
                    <tr data-grade="A">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Cloud Storage</td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td>
                            <div class="table-cell-content tooltip">
                                <i class="fas fa-link icon-blue"></i> Review Complete
                                <span class="tooltiptext">Security Review #12348 - All clear.</span>
                            </div>
                        </td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-green-500" style="width: 95%;">95%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-clock icon-purple"></i> Planned</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-green-600">A</div></td>
                    </tr>
                    <tr data-grade="C">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Compute Engine</td>
                        <td><div class="table-cell-content"><i class="fas fa-exclamation-triangle icon-yellow"></i> Fail</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-pencil-alt icon-gray"></i> Pending Review</div></td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-yellow-500 text-yellow-800" style="width: 88%;">88%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-clock icon-purple"></i> Planned</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-exclamation-triangle icon-yellow"></i> Fail</div></td>
                        <td><div class="table-cell-content font-bold text-yellow-600">C</div></td>
                    </tr>

                    <tr class="product-area-row">
                        <td colspan="7" class="sticky left-0 bg-gray-300 z-10">PRODUCTIVITY SUITE</td>
                    </tr>
                    <tr data-grade="A">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Google Docs</td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td>
                            <div class="table-cell-content tooltip">
                                <i class="fas fa-link icon-blue"></i> Review Complete
                                <span class="tooltiptext">Security Review #12349 - All clear.</span>
                            </div>
                        </td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-green-500" style="width: 96%;">96%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Fully Enforced</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-green-600">A</div></td>
                    </tr>
                    <tr data-grade="D">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Google Sheets</td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-pencil-alt icon-gray"></i> Pending Review</div></td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar bg-orange-500" style="width: 79%;">79%</div>
                            </div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-times-circle icon-red"></i> Not Enforced</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-check-circle icon-green"></i> Pass</div></td>
                        <td><div class="table-cell-content font-bold text-orange-600">D</div></td>
                    </tr>
                    <tr data-grade="C">
                        <td class="font-medium text-slate-700 sticky left-0 bg-white z-10">Google Slides</td>
                        <td><div class="table-cell-content"><i class="fas fa-minus-circle icon-gray"></i> N/A</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-minus-circle icon-gray"></i> N/A</div></td>
                        <td>
                           <div class="table-cell-content"><i class="fas fa-minus-circle icon-gray"></i> No Data</div>
                        </td>
                        <td><div class="table-cell-content"><i class="fas fa-clock icon-purple"></i> Planned</div></td>
                        <td><div class="table-cell-content"><i class="fas fa-exclamation-triangle icon-yellow"></i> Partial</div></td>
                        <td><div class="table-cell-content font-bold text-yellow-600">C</div></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <footer class="mt-12 text-center text-sm text-slate-500">
            <p>&copy; <span id="currentYear"></span> Your Company | Product Security Scorecard v1.1</p>
            <p>Last Updated: <span id="lastUpdated"></span> (Illustrative)</p>
        </footer>
    </div>

    <script>
        document.getElementById('currentYear').textContent = new Date().getFullYear();
        // Simulate last updated date
        const today = new Date();
        document.getElementById('lastUpdated').textContent = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

        // JavaScript for filtering table rows
        document.addEventListener('DOMContentLoaded', () => {
            const gradeFilter = document.getElementById('gradeFilter');
            const tableBody = document.querySelector('table tbody');
            const rows = Array.from(tableBody.querySelectorAll('tr')); // Convert NodeList to Array for easier filtering

            // Add a data-grade attribute to each product row for easier filtering
            // This is done in the HTML already, but this demonstrates how to add dynamically if needed.
            rows.forEach(row => {
                if (!row.classList.contains('product-area-row') && !row.classList.contains('metric-maturity-row')) {
                    const gradeCell = row.querySelector('td:last-child');
                    if (gradeCell) {
                        const grade = gradeCell.textContent.trim();
                        row.dataset.grade = grade; // Store the grade in a data attribute
                    }
                }
            });

            // Function to filter table rows based on selected grade
            const filterTableByGrade = () => {
                const selectedGrade = gradeFilter.value; // Get the selected value from the dropdown

                rows.forEach(row => {
                    // Always show product area headers and metric maturity row
                    if (row.classList.contains('product-area-row') || row.classList.contains('metric-maturity-row')) {
                        row.style.display = ''; // Ensure these rows are always visible
                    } else {
                        // For regular data rows, check their grade
                        const rowGrade = row.dataset.grade;
                        if (selectedGrade === 'All' || rowGrade === selectedGrade) {
                            row.style.display = ''; // Show the row
                        } else {
                            row.style.display = 'none'; // Hide the row
                        }
                    }
                });
            };

            // Attach the filter function to the 'change' event of the dropdown
            gradeFilter.addEventListener('change', filterTableByGrade);

            // Initial filter application in case a default value is set or for consistency
            filterTableByGrade();
        });
    </script>

</body>
</html>
